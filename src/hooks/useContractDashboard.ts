/**
 * Custom hook for contract dashboard data management
 * Extracts data processing logic from ContractDashboard component
 */

import { useState, useEffect } from "react";
import { useApi, ContractService } from "@/lib/api-client";
import { useWorkspace } from "@/lib/workspace-provider";
import type { Contract as ApiContract } from "@/services/api-types";

// Types for dashboard data
export interface DashboardStats {
  totalContracts: number;
  pendingApprovals: number;
  expiringSoon: number;
  complianceRate: number;
}

export interface RecentContract {
  id: string;
  title: string;
  type: string;
  status: string;
  createdBy: {
    name: string;
    avatar?: string;
    initials: string;
  };
  createdDate: string;
  counterparty: string;
}

export interface ExpiringContract {
  id: string;
  title: string;
  type: string;
  expiryDate: string;
  daysRemaining: number;
  counterparty: string;
}

export interface PendingApprovalContract {
  id: string;
  title: string;
  type: string;
  submittedBy: {
    name: string;
    avatar?: string;
    initials: string;
  };
  submittedDate: string;
  approvers: {
    name: string;
    status: string;
  }[];
}

export interface DashboardData {
  stats: DashboardStats;
  recentContracts: RecentContract[];
  expiringContracts: ExpiringContract[];
  pendingApprovalContracts: PendingApprovalContract[];
}

/**
 * Utility function to generate user avatar and initials
 */
function generateUserInfo(name?: string) {
  if (!name) {
    return {
      name: 'Unknown',
      initials: 'UN',
      avatar: undefined
    };
  }

  const initials = name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);

  const avatar = `https://api.dicebear.com/7.x/avataaars/svg?seed=${name.toLowerCase().replace(/\s/g, '')}`;

  return {
    name,
    initials,
    avatar
  };
}

/**
 * Process contracts data for dashboard display
 */
function processContractsData(contracts: ApiContract[]): DashboardData {
  const now = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(now.getDate() + 30);

  // Calculate stats
  const totalContracts = contracts.length;
  const pendingApprovals = contracts.filter(
    contract => contract.status === 'pending_approval'
  ).length;

  // Find expiring contracts
  const expiringContracts = contracts
    .filter(contract => {
      if (!contract.expiry_date) return false;
      const expiryDate = new Date(contract.expiry_date);
      return expiryDate > now && expiryDate <= thirtyDaysFromNow;
    })
    .map(contract => {
      const expiryDate = new Date(contract.expiry_date!);
      const daysRemaining = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      return {
        id: contract.id,
        title: contract.title,
        type: contract.type,
        expiryDate: contract.expiry_date!,
        daysRemaining,
        counterparty: contract.counterparty || 'N/A'
      };
    })
    .sort((a, b) => a.daysRemaining - b.daysRemaining)
    .slice(0, 3);

  // Find recent contracts
  const recentContracts = [...contracts]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 2)
    .map(contract => {
      const userInfo = generateUserInfo(contract.created_by?.name);
      
      return {
        id: contract.id,
        title: contract.title,
        type: contract.type,
        status: contract.status,
        createdBy: userInfo,
        createdDate: contract.created_at,
        counterparty: contract.counterparty || 'N/A'
      };
    });

  // Find pending approval contracts
  const pendingApprovalContracts = contracts
    .filter(contract => 
      contract.status === 'pending_approval' && 
      contract.approvers && 
      contract.approvers.length > 0
    )
    .slice(0, 2)
    .map(contract => {
      const userInfo = generateUserInfo(contract.created_by?.name);
      
      return {
        id: contract.id,
        title: contract.title,
        type: contract.type,
        submittedBy: userInfo,
        submittedDate: contract.created_at,
        approvers: contract.approvers!.map(approver => ({
          name: approver.name,
          status: approver.status
        }))
      };
    });

  // Calculate compliance rate (simplified calculation)
  const activeContracts = contracts.filter(
    contract => contract.status === 'active'
  ).length;
  const complianceRate = activeContracts > 0
    ? Math.round((activeContracts / totalContracts) * 100)
    : 0;

  return {
    stats: {
      totalContracts,
      pendingApprovals,
      expiringSoon: expiringContracts.length,
      complianceRate
    },
    recentContracts,
    expiringContracts,
    pendingApprovalContracts
  };
}

/**
 * Custom hook for contract dashboard data
 */
export function useContractDashboard() {
  const { fetchArray } = useApi();
  const { currentWorkspace } = useWorkspace();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    stats: {
      totalContracts: 0,
      pendingApprovals: 0,
      expiringSoon: 0,
      complianceRate: 0
    },
    recentContracts: [],
    expiringContracts: [],
    pendingApprovalContracts: []
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!currentWorkspace) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const params = {
          workspace_id: currentWorkspace.id,
        };

        const contractsResult = await fetchArray(
          () => ContractService.getContracts(params),
          "Loading dashboard data...",
          "Failed to load dashboard data"
        );

        if (contractsResult && contractsResult.length > 0) {
          const processedData = processContractsData(contractsResult);
          setDashboardData(processedData);
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentWorkspace?.id, fetchArray]);

  return {
    dashboardData,
    loading,
    error,
    refetch: () => {
      // Trigger a refetch by updating the dependency
      if (currentWorkspace) {
        setLoading(true);
      }
    }
  };
}

/**
 * Utility function for date formatting
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

/**
 * Utility function for status badge styling
 */
export function getStatusBadgeVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status.toLowerCase()) {
    case 'active':
      return 'default';
    case 'pending_approval':
      return 'secondary';
    case 'expired':
    case 'terminated':
      return 'destructive';
    default:
      return 'outline';
  }
}
