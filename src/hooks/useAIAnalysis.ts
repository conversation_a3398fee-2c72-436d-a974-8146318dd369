/**
 * Custom hook for AI analysis logic
 * Extracts data processing and API logic from ContractAIAnalysisDashboard component
 */

import { useState, useEffect } from "react";
import { useApi, AIAnalysisService } from "@/lib/api-client";
import type { AIAnalysisResult } from "@/services/api-types";
import { useToast } from "@/components/ui/use-toast";

// Types for component data structures
export interface Clause {
  id: string;
  name: string;
  category: string;
  riskLevel: "high" | "medium" | "low" | "none";
  description: string;
  recommendation?: string;
}

export interface Obligation {
  id: string;
  title: string;
  dueDate: string;
  status: "pending" | "completed" | "overdue";
  assignedTo?: string;
  description: string;
}

export interface ComplianceItem {
  id: string;
  regulation: string;
  status: "compliant" | "non-compliant" | "warning";
  details: string;
}

export interface ProcessedAnalysisData {
  riskScore: number;
  clauses: Clause[];
  obligations: Obligation[];
  complianceItems: ComplianceItem[];
}

/**
 * Transform API analysis result to component data structures
 */
function processAnalysisData(analysis: AIAnalysisResult | null): ProcessedAnalysisData {
  if (!analysis) {
    return {
      riskScore: 0,
      clauses: [],
      obligations: [],
      complianceItems: []
    };
  }

  const clauses: Clause[] = analysis.extracted_clauses.map(clause => ({
    id: clause.id,
    name: clause.title,
    category: clause.category || 'general',
    riskLevel: (clause.risk_level as any) || 'none',
    description: clause.content,
    recommendation: analysis.key_risks.find(risk =>
      risk.location === clause.location
    )?.recommendation
  }));

  const obligations: Obligation[] = analysis.obligations.map(obl => ({
    id: obl.id,
    title: obl.title,
    dueDate: obl.due_date,
    status: obl.status,
    assignedTo: obl.assigned_to,
    description: obl.description
  }));

  const complianceItems: ComplianceItem[] = analysis.compliance_issues.map(issue => ({
    id: issue.id,
    regulation: issue.regulation || 'General',
    status: issue.status,
    details: issue.details
  }));

  return {
    riskScore: analysis.risk_score || 0,
    clauses,
    obligations,
    complianceItems
  };
}

/**
 * Custom hook for AI analysis data management
 */
export function useAIAnalysis(contractId?: string) {
  const { fetch } = useApi();
  const { toast } = useToast();

  const [analysis, setAnalysis] = useState<AIAnalysisResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRunningAnalysis, setIsRunningAnalysis] = useState(false);

  // Process the raw analysis data
  const processedData = processAnalysisData(analysis);

  // Fetch analysis data when component mounts
  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!contractId) return;

      setLoading(true);
      setError(null);

      try {
        const result = await fetch(
          () => AIAnalysisService.getAnalysis(contractId),
          "Loading AI analysis...",
          "Failed to load AI analysis"
        );

        if (result) {
          setAnalysis(result);
        }
      } catch (err) {
        console.error("Error fetching AI analysis:", err);
        setError("Failed to load AI analysis. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();
  }, [contractId, fetch]);

  // Run fresh analysis
  const handleRunAnalysis = async () => {
    if (!contractId) return;

    setIsRunningAnalysis(true);
    setError(null);

    try {
      const result = await fetch(
        () => AIAnalysisService.runAnalysis({ contract_id: contractId }),
        "Running AI analysis...",
        "Failed to run AI analysis"
      );

      if (result) {
        setAnalysis(result);
        toast({
          title: "Analysis Complete",
          description: "AI analysis has been updated with the latest insights.",
        });
      }
    } catch (err) {
      console.error("Error running AI analysis:", err);
      setError("Failed to run AI analysis. Please try again later.");
      toast({
        title: "Analysis Failed",
        description: "Unable to complete AI analysis. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRunningAnalysis(false);
    }
  };

  // Export analysis
  const handleExportAnalysis = async () => {
    if (!contractId) return;

    try {
      await fetch(
        () => AIAnalysisService.exportAnalysis(contractId),
        "Exporting analysis...",
        "Failed to export analysis"
      );

      toast({
        title: "Export Started",
        description: "Your analysis export will be ready shortly.",
      });
    } catch (err) {
      console.error("Error exporting analysis:", err);
      toast({
        title: "Export Failed",
        description: "Unable to export analysis. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    // Raw data
    analysis,
    
    // Processed data
    ...processedData,
    
    // State
    loading,
    error,
    isRunningAnalysis,
    
    // Actions
    handleRunAnalysis,
    handleExportAnalysis,
    
    // Computed values
    hasData: analysis !== null,
  };
}
