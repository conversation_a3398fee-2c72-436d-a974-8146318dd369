/**
 * Custom hook for contract creation logic
 * Extracts business logic from ContractCreationMethod component
 */

import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

export interface ContractCreationState {
  activeTab: string;
  aiPrompt: string;
  isGenerating: boolean;
  showImportModal: boolean;
}

export function useContractCreation() {
  const navigate = useNavigate();
  const location = useLocation();
  
  const [state, setState] = useState<ContractCreationState>({
    activeTab: "template",
    aiPrompt: "",
    isGenerating: false,
    showImportModal: false,
  });

  // Check if we're on the import route and show the import modal
  useEffect(() => {
    if (location.pathname === "/contracts/import") {
      setState(prev => ({ ...prev, showImportModal: true }));
    }
  }, [location.pathname]);

  // Navigation handlers
  const handleUseTemplate = () => {
    navigate("/app/contracts/templates");
  };

  const handleStartFromScratch = () => {
    navigate("/app/contracts/wizard");
  };

  const handleGoBack = () => {
    navigate("/contracts");
  };

  // Import modal handlers
  const handleImportModalChange = (open: boolean) => {
    setState(prev => ({ ...prev, showImportModal: open }));
    // If we're on the import route and the modal is closed, navigate back to create
    if (!open && location.pathname === "/app/contracts/import") {
      navigate("/app/contracts/create");
    }
  };

  // AI generation handlers
  const handleAIPromptChange = (prompt: string) => {
    setState(prev => ({ ...prev, aiPrompt: prompt }));
  };

  const handleAIGenerate = async () => {
    if (!state.aiPrompt.trim()) return;

    setState(prev => ({ ...prev, isGenerating: true }));
    
    try {
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For now, just log the request
      // In the future, this would generate the contract directly
      console.log("AI Generation requested:", state.aiPrompt);
      
      // TODO: Implement actual AI generation
      // const generatedContract = await generateContractWithAI(state.aiPrompt);
      // navigate("/app/contracts/wizard", { state: { generatedContract } });
      
    } catch (error) {
      console.error("AI generation failed:", error);
      // TODO: Show error toast
    } finally {
      setState(prev => ({ ...prev, isGenerating: false }));
    }
  };

  // Tab change handler
  const handleTabChange = (tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab }));
  };

  // Template category selection
  const handleCategorySelect = (category: string) => {
    console.log("Template category selected:", category);
    // TODO: Navigate to templates with category filter
    navigate(`/app/contracts/templates?category=${category}`);
  };

  return {
    // State
    ...state,
    
    // Actions
    handleUseTemplate,
    handleStartFromScratch,
    handleGoBack,
    handleImportModalChange,
    handleAIPromptChange,
    handleAIGenerate,
    handleTabChange,
    handleCategorySelect,
    
    // Computed values
    canGenerateAI: state.aiPrompt.trim().length > 0,
  };
}
