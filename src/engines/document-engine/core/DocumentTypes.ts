// Core types and interfaces for the Document Engine
import { Editor } from '@tiptap/react';

// Document Engine Configuration
export interface DocumentEngineConfig {
  mode: 'wizard' | 'standalone' | 'collaborative';
  theme?: DocumentTheme;
  toolbar?: ToolbarConfig;
  collaboration?: CollaborationConfig;
  permissions?: DocumentPermissions;
  export?: ExportConfig;
}

// Document Theme Configuration
export interface DocumentTheme {
  fontFamily?: string;
  fontSize?: string;
  lineHeight?: number;
  pageWidth?: string;
  margins?: {
    top: string;
    right: string;
    bottom: string;
    left: string;
  };
  colors?: {
    text: string;
    background: string;
    border: string;
    accent: string;
  };
}

// Toolbar Configuration
export interface ToolbarConfig {
  simplified?: boolean;
  sections?: ToolbarSection[];
  customButtons?: CustomToolbarButton[];
  hiddenFeatures?: string[];
}

export interface ToolbarSection {
  id: string;
  label: string;
  items: ToolbarItem[];
  collapsible?: boolean;
}

export interface ToolbarItem {
  id: string;
  type: 'button' | 'dropdown' | 'separator' | 'custom';
  icon?: React.ComponentType;
  label?: string;
  action?: () => void;
  isActive?: () => boolean;
  isDisabled?: () => boolean;
}

export interface CustomToolbarButton {
  id: string;
  icon: React.ComponentType;
  label: string;
  action: (editor: Editor) => void;
  position: 'start' | 'end' | number;
}

// Document Structure
export interface DocumentStructure {
  sections: DocumentSection[];
  outline: OutlineNode[];
  metadata: DocumentMetadata;
}

export interface DocumentSection {
  id: string;
  type: 'heading' | 'paragraph' | 'list' | 'table' | 'clause' | 'signature';
  level?: number;
  numbering?: string;
  title?: string;
  content: string;
  metadata?: SectionMetadata;
}

export interface OutlineNode {
  id: string;
  title: string;
  level: number;
  numbering: string;
  children: OutlineNode[];
  sectionId: string;
}

export interface SectionMetadata {
  created: Date;
  modified: Date;
  author: string;
  locked?: boolean;
  comments?: Comment[];
}

export interface DocumentMetadata {
  id: string;
  title: string;
  created: Date;
  modified: Date;
  version: string;
  authors: string[];
  tags: string[];
  template?: string;
}

// Export Configuration
export interface ExportConfig {
  formats: ExportFormat[];
  defaultFormat: ExportFormat;
  customOptions?: Record<string, unknown>;
}

export type ExportFormat = 'pdf' | 'docx' | 'html' | 'txt' | 'json';

export interface ExportOptions {
  format: ExportFormat;
  includeComments?: boolean;
  includeMetadata?: boolean;
  pageSize?: 'A4' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
}

// Collaboration Types (for future implementation)
export interface CollaborationConfig {
  enabled: boolean;
  roomId?: string;
  websocketUrl?: string;
  conflictResolution?: 'manual' | 'automatic' | 'last-writer-wins';
}

export interface DocumentPermissions {
  userId: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  sectionPermissions?: Record<string, Permission>;
  timeBasedAccess?: {
    start: Date;
    end: Date;
  };
}

export type Permission = 'read' | 'write' | 'comment' | 'approve' | 'none';

// Real-time Collaboration Types (placeholders)
export interface DocumentOperation {
  id: string;
  type: 'insert' | 'delete' | 'format' | 'structure';
  position: number;
  content?: unknown;
  userId: string;
  timestamp: Date;
  metadata?: OperationMetadata;
}

export interface OperationMetadata {
  source: 'user' | 'system' | 'sync';
  device?: string;
  sessionId?: string;
}

export interface UserPresence {
  userId: string;
  name: string;
  avatar?: string;
  cursor?: {
    position: number;
    selection?: {
      from: number;
      to: number;
    };
  };
  lastSeen: Date;
  isActive: boolean;
}

// Comment System Types
export interface Comment {
  id: string;
  content: string;
  author: string;
  created: Date;
  modified?: Date;
  resolved?: boolean;
  replies?: Comment[];
  position: {
    from: number;
    to: number;
  };
  metadata?: CommentMetadata;
}

export interface CommentMetadata {
  type: 'suggestion' | 'question' | 'approval' | 'general';
  priority?: 'low' | 'medium' | 'high';
  tags?: string[];
}

// Document Engine Events
export interface DocumentEngineEvents {
  onContentChange?: (content: string) => void;
  onStructureChange?: (structure: DocumentStructure) => void;
  onSave?: (document: DocumentData) => Promise<void>;
  onExport?: (format: ExportFormat, data: Blob) => void;
  onError?: (error: DocumentEngineError) => void;
  onCollaboratorJoin?: (user: UserPresence) => void;
  onCollaboratorLeave?: (userId: string) => void;
  onCommentAdd?: (comment: Comment) => void;
  onCommentResolve?: (commentId: string) => void;
}

export interface DocumentData {
  id: string;
  content: string;
  structure: DocumentStructure;
  metadata: DocumentMetadata;
  version: string;
}

export interface DocumentEngineError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: Date;
}

// Hook Return Types
export interface UseDocumentEngineReturn {
  editor: Editor | null;
  isReady: boolean;
  content: string;
  structure: DocumentStructure;
  isLoading: boolean;
  error: DocumentEngineError | null;
  
  // Actions
  setContent: (content: string) => void;
  exportDocument: (options: ExportOptions) => Promise<Blob>;
  save: (saveOptions?: { 
    onSave?: (documentData: { content: string; metadata: DocumentMetadata }) => Promise<void>;
    contractId?: string;
    workspaceId?: string;
  }) => Promise<{ content: string; metadata: DocumentMetadata } | undefined>;
  undo: () => void;
  redo: () => void;
  
  // Collaboration (future)
  collaborators: UserPresence[];
  comments: Comment[];
  addComment: (content: string, position: { from: number; to: number }) => void;
  resolveComment: (commentId: string) => void;
}
