import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@clerk/clerk-react';
import { ContractService } from '@/lib/api-client';
import {
  Download,
  FileText,
  File,
  Globe,
  Code,
  Loader2,
  CheckCircle,
  XCircle,
  Clock,
  ExternalLink,
  MoreHorizontal,
  Trash2,
  Archive,
  Eye,
} from 'lucide-react';

// Unified export types
export type ExportFormat = 'pdf' | 'docx' | 'html' | 'txt' | 'markdown';
export type ExportMode = 'single' | 'batch';

export interface ExportOption {
  format: ExportFormat;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  contentType: string;
}

export interface ExportConfig {
  format: ExportFormat;
  includeAttachments?: boolean;
  includeVersionHistory?: boolean;
  includeComments?: boolean;
  includeMetadata?: boolean;
  templateId?: string;
  brandingSettings?: any;
}

export interface ExportResult {
  contractId: string;
  success: boolean;
  filename?: string;
  url?: string;
  error?: string;
  fileSize?: number;
}

export interface ExportRecord {
  id: string;
  contractId: string;
  contractTitle: string;
  format: string;
  fileSize: number;
  downloadUrl: string;
  exportedBy: {
    id: string;
    name: string;
  };
  exportedAt: string;
  expiresAt?: string;
  downloadCount: number;
  status: 'active' | 'expired' | 'archived';
  templateUsed?: string;
}

interface UnifiedExportManagerProps {
  // Single export mode
  contractId?: string;
  contractTitle?: string;
  
  // Batch export mode
  contractIds?: string[];
  contractTitles?: string[];
  
  // Display options
  mode?: ExportMode;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  showLabel?: boolean;
  showHistory?: boolean;
  
  // Callbacks
  onExportComplete?: (results: ExportResult[]) => void;
  onExportError?: (error: string) => void;
}

const exportOptions: ExportOption[] = [
  {
    format: 'pdf',
    label: 'PDF Document',
    description: 'Professional PDF with formatting',
    icon: FileText,
    contentType: 'application/pdf',
  },
  {
    format: 'docx',
    label: 'Word Document',
    description: 'Editable Microsoft Word format',
    icon: File,
    contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  },
  {
    format: 'html',
    label: 'HTML Document',
    description: 'Web-ready HTML format',
    icon: Globe,
    contentType: 'text/html',
  },
  {
    format: 'txt',
    label: 'Plain Text',
    description: 'Simple text format',
    icon: FileText,
    contentType: 'text/plain',
  },
  {
    format: 'markdown',
    label: 'Markdown',
    description: 'Markdown format for documentation',
    icon: Code,
    contentType: 'text/markdown',
  },
];

const UnifiedExportManager: React.FC<UnifiedExportManagerProps> = ({
  contractId,
  contractTitle = 'Contract',
  contractIds = [],
  contractTitles = [],
  mode = 'single',
  variant = 'outline',
  size = 'default',
  className = '',
  showLabel = true,
  showHistory = false,
  onExportComplete,
  onExportError,
}) => {
  // State management
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('pdf');
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    format: 'pdf',
    includeAttachments: true,
    includeVersionHistory: false,
    includeComments: false,
    includeMetadata: true,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportResults, setExportResults] = useState<ExportResult[]>([]);
  const [exportHistory, setExportHistory] = useState<ExportRecord[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  const { toast } = useToast();
  const { getToken } = useAuth();

  // Determine export targets
  const targets = mode === 'batch' 
    ? contractIds 
    : contractId ? [contractId] : [];
  
  const targetTitles = mode === 'batch' 
    ? contractTitles 
    : contractTitle ? [contractTitle] : [];

  // Load export history
  useEffect(() => {
    if (showHistory && (contractId || contractIds.length > 0)) {
      loadExportHistory();
    }
  }, [showHistory, contractId, contractIds]);

  const loadExportHistory = async () => {
    try {
      // Implementation would load from API
      // For now, using mock data
      const mockHistory: ExportRecord[] = [
        {
          id: 'export-1',
          contractId: contractId || contractIds[0] || '',
          contractTitle: contractTitle || contractTitles[0] || 'Contract',
          format: 'PDF',
          fileSize: 245760,
          downloadUrl: 'https://example.com/exports/contract.pdf',
          exportedBy: { id: 'user-1', name: 'John Doe' },
          exportedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          expiresAt: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
          downloadCount: 3,
          status: 'active',
          templateUsed: 'Professional Template',
        },
      ];
      setExportHistory(mockHistory);
    } catch (error) {
      console.error('Failed to load export history:', error);
    }
  };

  const handleExport = async () => {
    if (targets.length === 0) {
      toast({
        title: "No contracts selected",
        description: "Please select at least one contract to export.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);
    setExportResults([]);

    try {
      const token = await getToken();
      const config = { ...exportConfig, format: selectedFormat };

      let results: ExportResult[] = [];

      if (mode === 'single' && contractId) {
        // Single export
        const response = await ContractService.generateDocument(
          contractId,
          selectedFormat,
          config.templateId,
          token || undefined
        );

        if (response.data && response.data.file_info) {
          results = [{
            contractId,
            success: true,
            filename: response.data.file_info.filename,
            url: response.data.file_info.url,
            fileSize: response.data.file_info.size,
          }];
        } else {
          results = [{
            contractId,
            success: false,
            error: response.message || 'Export failed',
          }];
        }
      } else if (mode === 'batch' && contractIds.length > 0) {
        // Batch export
        const response = await ContractService.batchGenerateDocuments(
          contractIds,
          selectedFormat,
          token || undefined
        );

        if (response.data && response.data.results) {
          results = response.data.results.map((result: any) => ({
            contractId: result.contract_id,
            success: result.success,
            filename: result.file_info?.filename,
            url: result.file_info?.url,
            fileSize: result.file_info?.size,
            error: result.error,
          }));
        }
      }

      setExportResults(results);
      setShowResults(true);

      // Handle success/failure
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (successful.length > 0) {
        toast({
          title: "Export completed",
          description: `Successfully exported ${successful.length} contract${successful.length !== 1 ? 's' : ''}`,
        });

        if (onExportComplete) {
          onExportComplete(results);
        }
      }

      if (failed.length > 0) {
        toast({
          title: "Some exports failed",
          description: `${failed.length} export${failed.length !== 1 ? 's' : ''} failed. Check the results for details.`,
          variant: "destructive",
        });

        if (onExportError) {
          onExportError(`${failed.length} exports failed`);
        }
      }

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export failed",
        description: "An error occurred during export. Please try again.",
        variant: "destructive",
      });

      if (onExportError) {
        onExportError(error instanceof Error ? error.message : 'Export failed');
      }
    } finally {
      setIsExporting(false);
      setExportProgress(100);
    }
  };

  const handleDownload = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Render main export button
  const renderExportButton = () => {
    const isDisabled = targets.length === 0 || isExporting;
    const buttonText = mode === 'batch' 
      ? `Export ${targets.length} Contract${targets.length !== 1 ? 's' : ''}` 
      : 'Export Contract';

    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsDialogOpen(true)}
        disabled={isDisabled}
      >
        {isExporting ? (
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
        ) : (
          <Download className="h-4 w-4 mr-2" />
        )}
        {showLabel && (isExporting ? 'Exporting...' : buttonText)}
      </Button>
    );
  };

  return (
    <>
      {/* Main Export Button */}
      {renderExportButton()}

      {/* Export History Button */}
      {showHistory && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowHistoryDialog(true)}
          className="ml-2"
        >
          <Clock className="h-4 w-4 mr-2" />
          History
        </Button>
      )}

      {/* Export Configuration Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              Export {mode === 'batch' ? `${targets.length} Contracts` : 'Contract'}
            </DialogTitle>
            <DialogDescription>
              Choose your export format and options
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Format Selection */}
            <div>
              <Label>Export Format</Label>
              <Select value={selectedFormat} onValueChange={(value: ExportFormat) => {
                setSelectedFormat(value);
                setExportConfig(prev => ({ ...prev, format: value }));
              }}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {exportOptions.map((option) => {
                    const Icon = option.icon;
                    return (
                      <SelectItem key={option.format} value={option.format}>
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-muted-foreground">
                              {option.description}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Export Options */}
            <div className="space-y-3">
              <Label>Export Options</Label>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="attachments"
                  checked={exportConfig.includeAttachments}
                  onCheckedChange={(checked) =>
                    setExportConfig(prev => ({ ...prev, includeAttachments: !!checked }))
                  }
                />
                <Label htmlFor="attachments" className="text-sm">
                  Include attachments
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="metadata"
                  checked={exportConfig.includeMetadata}
                  onCheckedChange={(checked) =>
                    setExportConfig(prev => ({ ...prev, includeMetadata: !!checked }))
                  }
                />
                <Label htmlFor="metadata" className="text-sm">
                  Include metadata
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="comments"
                  checked={exportConfig.includeComments}
                  onCheckedChange={(checked) =>
                    setExportConfig(prev => ({ ...prev, includeComments: !!checked }))
                  }
                />
                <Label htmlFor="comments" className="text-sm">
                  Include comments
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="history"
                  checked={exportConfig.includeVersionHistory}
                  onCheckedChange={(checked) =>
                    setExportConfig(prev => ({ ...prev, includeVersionHistory: !!checked }))
                  }
                />
                <Label htmlFor="history" className="text-sm">
                  Include version history
                </Label>
              </div>
            </div>

            {/* Progress Bar */}
            {isExporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Exporting...</span>
                  <span>{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
              disabled={isExporting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Results Dialog */}
      <Dialog open={showResults} onOpenChange={setShowResults}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Export Results</DialogTitle>
            <DialogDescription>
              {exportResults.filter(r => r.success).length} of {exportResults.length} exports completed successfully
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            {exportResults.map((result, index) => (
              <Card key={result.contractId} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {result.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <div>
                      <div className="font-medium">
                        {targetTitles[index] || `Contract ${result.contractId}`}
                      </div>
                      {result.success ? (
                        <div className="text-sm text-muted-foreground">
                          {result.filename} • {result.fileSize ? formatFileSize(result.fileSize) : 'Unknown size'}
                        </div>
                      ) : (
                        <div className="text-sm text-red-600">
                          {result.error || 'Export failed'}
                        </div>
                      )}
                    </div>
                  </div>
                  {result.success && result.url && (
                    <Button
                      size="sm"
                      onClick={() => handleDownload(result.url!, result.filename!)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  )}
                </div>
              </Card>
            ))}
          </div>

          <DialogFooter>
            <Button onClick={() => setShowResults(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export History Dialog */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Export History</DialogTitle>
            <DialogDescription>
              Recent exports for {mode === 'batch' ? 'selected contracts' : contractTitle}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {exportHistory.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No export history found</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {exportHistory.map((record) => (
                  <Card key={record.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                          <FileText className="h-5 w-5" />
                        </div>
                        <div>
                          <div className="font-medium">{record.contractTitle}</div>
                          <div className="text-sm text-muted-foreground">
                            {record.format} • {formatFileSize(record.fileSize)} •
                            Exported by {record.exportedBy.name} on {formatDate(record.exportedAt)}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant={record.status === 'active' ? 'default' : 'secondary'}>
                              {record.status}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              Downloaded {record.downloadCount} times
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {record.status === 'active' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownload(record.downloadUrl, `${record.contractTitle}.${record.format.toLowerCase()}`)}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <ExternalLink className="mr-2 h-4 w-4" />
                              Open Contract
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Archive className="mr-2 h-4 w-4" />
                              Archive
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowHistoryDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UnifiedExportManager;
