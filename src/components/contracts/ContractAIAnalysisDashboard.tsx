/**
 * Refactored Contract AI Analysis Dashboard Component
 * Now uses smaller, focused components and custom hooks for better maintainability
 */

import React, { useState } from "react";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs";
import { <PERSON><PERSON> } from "../ui/button";
import { FileText, Shield } from "lucide-react";

// Custom hook
import { useAIAnalysis } from "@/hooks/useAIAnalysis";

// Reusable components
import { RiskScoreCard } from "./ai-analysis/RiskScoreCard";
import { KeyIssuesCard } from "./ai-analysis/KeyIssuesCard";
import { ComplianceStatusCard } from "./ai-analysis/ComplianceStatusCard";
import { ClausesAnalysis } from "./ai-analysis/ClausesAnalysis";
import { ObligationsAnalysis } from "./ai-analysis/ObligationsAnalysis";
import { ComplianceAnalysis } from "./ai-analysis/ComplianceAnalysis";

interface ContractAIAnalysisDashboardProps {
  contractId?: string;
  contractName?: string;
  isFetching?: boolean;
}

const ContractAIAnalysisDashboard: React.FC<ContractAIAnalysisDashboardProps> = ({
  contractId,
  contractName,
}) => {
  // Use the custom hook for all AI analysis logic
  const {
    riskScore,
    clauses,
    obligations,
    complianceItems,
    loading,
    error,
    isRunningAnalysis,
    handleRunAnalysis,
    handleExportAnalysis,
  } = useAIAnalysis(contractId);

  const [activeTab, setActiveTab] = useState("overview");

  // Show loading state
  if (loading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <div className="w-48 h-6 bg-muted animate-pulse rounded mb-2" />
            <div className="w-32 h-4 bg-muted animate-pulse rounded" />
          </div>
          <div className="flex items-center gap-2">
            <div className="w-32 h-10 bg-muted animate-pulse rounded" />
            <div className="w-40 h-10 bg-muted animate-pulse rounded" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <RiskScoreCard riskScore={0} loading={true} />
          <KeyIssuesCard clauses={[]} obligations={[]} loading={true} />
          <ComplianceStatusCard complianceItems={[]} loading={true} />
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-red-600" />
          </div>
          <h3 className="text-lg font-medium mb-2">Analysis Error</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleRunAnalysis} disabled={isRunningAnalysis}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">{contractName}</h3>
          <p className="text-muted-foreground">AI Analysis & Insights</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportAnalysis}
          >
            <FileText className="mr-2 h-4 w-4" />
            Export Analysis
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleRunAnalysis}
            disabled={isRunningAnalysis}
          >
            {isRunningAnalysis ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Analyzing...
              </>
            ) : (
              <>
                <Shield className="mr-2 h-4 w-4" />
                Run Fresh Analysis
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="clauses">Clauses</TabsTrigger>
          <TabsTrigger value="obligations">Obligations</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <RiskScoreCard riskScore={riskScore} loading={loading} />
            <KeyIssuesCard clauses={clauses} obligations={obligations} loading={loading} />
            <ComplianceStatusCard complianceItems={complianceItems} loading={loading} />
          </div>
        </TabsContent>

        <TabsContent value="clauses" className="mt-4">
          <ClausesAnalysis clauses={clauses} loading={loading} />
        </TabsContent>

        <TabsContent value="obligations" className="mt-4">
          <ObligationsAnalysis obligations={obligations} loading={loading} />
        </TabsContent>

        <TabsContent value="compliance" className="mt-4">
          <ComplianceAnalysis complianceItems={complianceItems} loading={loading} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContractAIAnalysisDashboard;