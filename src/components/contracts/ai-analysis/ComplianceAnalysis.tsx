/**
 * Compliance Analysis Component
 * Displays detailed regulatory compliance assessment
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, AlertTriangle, Shield } from "lucide-react";

interface ComplianceItem {
  id: string;
  regulation: string;
  status: "compliant" | "non-compliant" | "warning";
  details: string;
}

interface ComplianceAnalysisProps {
  complianceItems: ComplianceItem[];
  loading?: boolean;
}

export function ComplianceAnalysis({ complianceItems, loading = false }: ComplianceAnalysisProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "compliant":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "non-compliant":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "default";
      case "non-compliant":
        return "destructive";
      case "warning":
        return "secondary";
      default:
        return "outline";
    }
  };

  const getStatusBgColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "bg-green-50 border-green-200";
      case "non-compliant":
        return "bg-red-50 border-red-200";
      case "warning":
        return "bg-amber-50 border-amber-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Compliance Checking</CardTitle>
          <CardDescription>Regulatory compliance assessment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-32 h-5 bg-muted animate-pulse rounded" />
                  <div className="w-16 h-6 bg-muted animate-pulse rounded" />
                </div>
                <div className="w-full h-4 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Compliance Checking</CardTitle>
        <CardDescription>Regulatory compliance assessment</CardDescription>
      </CardHeader>
      <CardContent>
        {complianceItems.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-medium mb-2">All Clear!</h3>
            <p className="text-muted-foreground">No compliance issues detected</p>
          </div>
        ) : (
          <div className="space-y-4">
            {complianceItems.map((item) => (
              <div key={item.id} className={`border rounded-lg p-4 ${getStatusBgColor(item.status)}`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(item.status)}
                    <h3 className="font-medium">{item.regulation}</h3>
                  </div>
                  <Badge variant={getStatusColor(item.status) as any}>
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                  </Badge>
                </div>
                <p className="text-sm">{item.details}</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
