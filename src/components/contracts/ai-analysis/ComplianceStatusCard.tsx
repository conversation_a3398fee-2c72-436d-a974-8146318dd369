/**
 * Compliance Status Card Component
 * Displays regulatory compliance overview
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, AlertTriangle } from "lucide-react";

interface ComplianceItem {
  id: string;
  regulation: string;
  status: "compliant" | "non-compliant" | "warning";
  details: string;
}

interface ComplianceStatusCardProps {
  complianceItems: ComplianceItem[];
  loading?: boolean;
}

export function ComplianceStatusCard({ complianceItems, loading = false }: ComplianceStatusCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "compliant":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "non-compliant":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "default";
      case "non-compliant":
        return "destructive";
      case "warning":
        return "secondary";
      default:
        return "outline";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Compliance Status</CardTitle>
          <CardDescription>Regulatory compliance overview</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-muted animate-pulse rounded" />
                  <div className="w-24 h-4 bg-muted animate-pulse rounded" />
                </div>
                <div className="w-16 h-6 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Compliance Status</CardTitle>
        <CardDescription>Regulatory compliance overview</CardDescription>
      </CardHeader>
      <CardContent>
        {complianceItems.length === 0 ? (
          <div className="text-center py-6">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <CheckCircle className="h-6 w-6 text-blue-600" />
            </div>
            <p className="text-sm text-muted-foreground">No compliance issues detected</p>
          </div>
        ) : (
          <ul className="space-y-2">
            {complianceItems.map(item => (
              <li key={item.id} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(item.status)}
                  <span className="text-sm font-medium">{item.regulation}</span>
                </div>
                <Badge variant={getStatusColor(item.status) as any}>
                  {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                </Badge>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}
