/**
 * Clauses Analysis Component
 * Displays detailed clause-by-clause analysis
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, AlertTriangle, Info } from "lucide-react";

interface Clause {
  id: string;
  name: string;
  category: string;
  riskLevel: "high" | "medium" | "low" | "none";
  description: string;
  recommendation?: string;
}

interface ClausesAnalysisProps {
  clauses: Clause[];
  loading?: boolean;
}

export function ClausesAnalysis({ clauses, loading = false }: ClausesAnalysisProps) {
  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case "high":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "medium":
        return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      case "low":
        return <Info className="h-4 w-4 text-blue-600" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "high":
        return "destructive";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "default";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Clause Analysis</CardTitle>
          <CardDescription>Detailed analysis of contract clauses</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-32 h-5 bg-muted animate-pulse rounded" />
                  <div className="w-16 h-6 bg-muted animate-pulse rounded" />
                </div>
                <div className="w-full h-4 bg-muted animate-pulse rounded mb-2" />
                <div className="w-3/4 h-4 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Clause Analysis</CardTitle>
        <CardDescription>Detailed analysis of contract clauses</CardDescription>
      </CardHeader>
      <CardContent>
        {clauses.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="h-8 w-8 text-gray-400" />
            </div>
            <p className="text-muted-foreground">No clauses analyzed yet</p>
          </div>
        ) : (
          <div className="space-y-4">
            {clauses.map((clause) => (
              <div key={clause.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getRiskIcon(clause.riskLevel)}
                    <h3 className="font-medium">{clause.name}</h3>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {clause.category}
                    </Badge>
                    <Badge variant={getRiskColor(clause.riskLevel) as any}>
                      {clause.riskLevel} risk
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {clause.description}
                </p>
                {clause.recommendation && (
                  <div className="bg-blue-50 border border-blue-200 rounded p-3 mt-2">
                    <p className="text-sm text-blue-800">
                      <strong>Recommendation:</strong> {clause.recommendation}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
