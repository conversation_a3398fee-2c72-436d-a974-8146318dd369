/**
 * Obligations Analysis Component
 * Displays contract obligations and deadlines
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, CheckCircle, Clock, AlertCircle } from "lucide-react";

interface Obligation {
  id: string;
  title: string;
  dueDate: string;
  status: "pending" | "completed" | "overdue";
  assignedTo?: string;
  description: string;
}

interface ObligationsAnalysisProps {
  obligations: Obligation[];
  loading?: boolean;
}

export function ObligationsAnalysis({ obligations, loading = false }: ObligationsAnalysisProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "overdue":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-amber-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "default";
      case "overdue":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Obligations & Deadlines</CardTitle>
          <CardDescription>Contract obligations and key dates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-32 h-5 bg-muted animate-pulse rounded" />
                  <div className="w-16 h-6 bg-muted animate-pulse rounded" />
                </div>
                <div className="w-full h-4 bg-muted animate-pulse rounded mb-2" />
                <div className="w-24 h-4 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Obligations & Deadlines</CardTitle>
        <CardDescription>Contract obligations and key dates</CardDescription>
      </CardHeader>
      <CardContent>
        {obligations.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar className="h-8 w-8 text-gray-400" />
            </div>
            <p className="text-muted-foreground">No obligations identified</p>
          </div>
        ) : (
          <div className="space-y-4">
            {obligations.map((obligation) => (
              <div key={obligation.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(obligation.status)}
                    <h3 className="font-medium">{obligation.title}</h3>
                  </div>
                  <Badge variant={getStatusColor(obligation.status) as any}>
                    {obligation.status.charAt(0).toUpperCase() + obligation.status.slice(1)}
                  </Badge>
                </div>
                
                <p className="text-sm text-muted-foreground mb-3">
                  {obligation.description}
                </p>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Due: {formatDate(obligation.dueDate)}</span>
                  </div>
                  
                  {obligation.assignedTo && (
                    <div className="text-muted-foreground">
                      Assigned to: {obligation.assignedTo}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
