/**
 * Risk Score Card Component
 * Displays the overall contract risk assessment
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface RiskScoreCardProps {
  riskScore: number;
  loading?: boolean;
}

export function RiskScoreCard({ riskScore, loading = false }: RiskScoreCardProps) {
  const getRiskColor = (score: number) => {
    if (score < 30) return "text-green-600";
    if (score < 70) return "text-amber-600";
    return "text-red-600";
  };

  const getRiskMessage = (score: number) => {
    if (score < 30) return "Low risk contract";
    if (score < 70) return "Medium risk - review recommended";
    return "High risk - thorough review required";
  };

  const getProgressColor = (score: number) => {
    if (score < 30) return "bg-green-600/20";
    if (score < 70) return "bg-amber-600/20";
    return "bg-red-600/20";
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Risk Score</CardTitle>
          <CardDescription>Overall contract risk assessment</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center flex-col">
            <div className="w-16 h-16 bg-muted animate-pulse rounded-full mb-2" />
            <div className="w-full h-2 bg-muted animate-pulse rounded mb-2" />
            <div className="w-32 h-4 bg-muted animate-pulse rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Risk Score</CardTitle>
        <CardDescription>Overall contract risk assessment</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-center flex-col">
          <div className={`text-5xl font-bold ${getRiskColor(riskScore)}`}>
            {riskScore}%
          </div>
          <Progress
            value={riskScore}
            max={100}
            className={cn("h-2 mt-2", getProgressColor(riskScore))}
          />
          <p className="text-sm text-muted-foreground mt-2">
            {getRiskMessage(riskScore)}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
