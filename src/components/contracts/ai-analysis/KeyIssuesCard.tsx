/**
 * Key Issues Card Component
 * Displays critical items requiring attention
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Clock } from "lucide-react";

interface Clause {
  id: string;
  name: string;
  category: string;
  riskLevel: "high" | "medium" | "low" | "none";
  description: string;
  recommendation?: string;
}

interface Obligation {
  id: string;
  title: string;
  dueDate: string;
  status: "pending" | "completed" | "overdue";
  assignedTo?: string;
  description: string;
}

interface KeyIssuesCardProps {
  clauses: Clause[];
  obligations: Obligation[];
  loading?: boolean;
}

export function KeyIssuesCard({ clauses, obligations, loading = false }: KeyIssuesCardProps) {
  const highRiskClauses = clauses.filter(clause => clause.riskLevel === "high");
  const overdueObligations = obligations.filter(obl => obl.status === "overdue");

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Key Issues</CardTitle>
          <CardDescription>Critical items requiring attention</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-start gap-2">
                <div className="w-5 h-5 bg-muted animate-pulse rounded mt-0.5" />
                <div className="flex-1 space-y-1">
                  <div className="w-32 h-4 bg-muted animate-pulse rounded" />
                  <div className="w-48 h-3 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const hasIssues = highRiskClauses.length > 0 || overdueObligations.length > 0;

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Key Issues</CardTitle>
        <CardDescription>Critical items requiring attention</CardDescription>
      </CardHeader>
      <CardContent>
        {!hasIssues ? (
          <div className="text-center py-6">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <AlertCircle className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-sm text-muted-foreground">No critical issues found</p>
          </div>
        ) : (
          <ul className="space-y-2">
            {highRiskClauses.map(clause => (
              <li key={clause.id} className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">{clause.name}</p>
                  <p className="text-sm text-muted-foreground">{clause.recommendation}</p>
                </div>
              </li>
            ))}
            {overdueObligations.map(obl => (
              <li key={obl.id} className="flex items-start gap-2">
                <Clock className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">{obl.title}</p>
                  <p className="text-sm text-muted-foreground">Overdue: {obl.dueDate}</p>
                </div>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}
