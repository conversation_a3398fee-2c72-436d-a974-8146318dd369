/**
 * Reusable Feature List Component
 * Displays a list of features with icons and descriptions
 */

import { LucideIcon } from "lucide-react";

interface Feature {
  icon: LucideIcon;
  label: string;
  color: string;
}

interface FeatureListProps {
  features: Feature[];
  className?: string;
}

export function FeatureList({ features, className = "" }: FeatureListProps) {
  return (
    <div className={`flex items-center gap-3 text-xs text-muted-foreground ${className}`}>
      {features.map((feature, index) => (
        <div key={index} className="flex items-center gap-1">
          <feature.icon className={`h-3 w-3 ${feature.color}`} />
          <span>{feature.label}</span>
        </div>
      ))}
    </div>
  );
}
