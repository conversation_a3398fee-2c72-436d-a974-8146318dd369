/**
 * Secondary Options Component
 * Displays alternative contract creation methods
 */

import { Button } from "@/components/ui/button";
import { Import, Copy, Users } from "lucide-react";

interface SecondaryOptionsProps {
  onImport: () => void;
  onClone: () => void;
  onRequestHelp: () => void;
}

export function SecondaryOptions({ onImport, onClone, onRequestHelp }: SecondaryOptionsProps) {
  const options = [
    {
      icon: Import,
      title: "Import Document",
      description: "Upload existing files",
      onClick: onImport,
    },
    {
      icon: Copy,
      title: "Clone Contract",
      description: "Copy existing contract",
      onClick: onClone,
    },
    {
      icon: Users,
      title: "Request Help",
      description: "Get legal team assistance",
      onClick: onRequestHelp,
    },
  ];

  return (
    <div className="mt-12 pt-8 border-t border-border">
      <div className="text-center mb-6">
        <h4 className="text-lg font-medium mb-2">Other Ways to Create</h4>
        <p className="text-sm text-muted-foreground">Alternative methods for specific needs</p>
      </div>

      <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
        {options.map((option, index) => (
          <Button
            key={index}
            variant="outline"
            className="h-auto py-4 flex flex-col items-center gap-2 hover:border-primary/50"
            onClick={option.onClick}
          >
            <option.icon className="h-6 w-6 text-muted-foreground" />
            <div className="text-center">
              <div className="font-medium text-sm">{option.title}</div>
              <div className="text-xs text-muted-foreground">{option.description}</div>
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}
