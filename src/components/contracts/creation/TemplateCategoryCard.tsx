/**
 * Reusable Template Category Card Component
 * Extracted from ContractCreationMethod to reduce duplication
 */

import { Card } from "@/components/ui/card";

interface TemplateCategoryCardProps {
  emoji: string;
  title: string;
  description: string;
  bgColor: string;
  onClick?: () => void;
}

export function TemplateCategoryCard({
  emoji,
  title,
  description,
  bgColor,
  onClick,
}: TemplateCategoryCardProps) {
  return (
    <Card 
      className="border border-muted p-3 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center gap-2 mb-1">
        <div className={`w-5 h-5 rounded ${bgColor} flex items-center justify-center`}>
          <span className="text-xs">{emoji}</span>
        </div>
        <div className="font-medium text-sm">{title}</div>
      </div>
      <div className="text-xs text-muted-foreground">{description}</div>
    </Card>
  );
}

/**
 * Template Categories Grid Component
 * Groups template category cards in a responsive grid
 */
interface TemplateCategoriesGridProps {
  onCategorySelect?: (category: string) => void;
}

export function TemplateCategoriesGrid({ onCategorySelect }: TemplateCategoriesGridProps) {
  const categories = [
    {
      emoji: "🔒",
      title: "NDA",
      description: "Confidentiality protection",
      bgColor: "bg-green-100",
      key: "nda"
    },
    {
      emoji: "🛠️",
      title: "Services",
      description: "Service agreements",
      bgColor: "bg-blue-100",
      key: "services"
    },
    {
      emoji: "👤",
      title: "Employment",
      description: "Hiring contracts",
      bgColor: "bg-purple-100",
      key: "employment"
    },
    {
      emoji: "⚖️",
      title: "License",
      description: "Software licensing",
      bgColor: "bg-amber-100",
      key: "license"
    }
  ];

  return (
    <div className="grid grid-cols-2 gap-3 mb-6">
      {categories.map((category) => (
        <TemplateCategoryCard
          key={category.key}
          emoji={category.emoji}
          title={category.title}
          description={category.description}
          bgColor={category.bgColor}
          onClick={() => onCategorySelect?.(category.key)}
        />
      ))}
    </div>
  );
}
