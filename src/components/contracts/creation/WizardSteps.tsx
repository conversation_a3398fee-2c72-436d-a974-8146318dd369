/**
 * Wizard Steps Component
 * Displays the step-by-step process for contract creation
 */

interface WizardStepsProps {
  totalSteps?: number;
}

export function WizardSteps({ totalSteps = 7 }: WizardStepsProps) {
  const steps = [
    "Jurisdiction & type",
    "Parties information", 
    "Contract terms",
    "Legal clauses",
    "Industry provisions",
    "Attachments",
    "Review & approval"
  ];

  return (
    <div className="bg-secondary/20 p-4 rounded-lg mb-6">
      <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
        <span className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary">
          {totalSteps}
        </span>
        Step-by-step wizard
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-muted-foreground">
        {steps.map((step, index) => (
          <div 
            key={index} 
            className={`flex items-center gap-2 ${index === steps.length - 1 ? 'md:col-span-2' : ''}`}
          >
            <div className="w-4 h-4 rounded-full bg-primary/10 flex items-center justify-center">
              <span className="text-xs font-medium text-primary">{index + 1}</span>
            </div>
            <span>{step}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
