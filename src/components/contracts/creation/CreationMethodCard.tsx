/**
 * Reusable Creation Method Card Component
 * Extracted from ContractCreationMethod to reduce duplication
 */

import { ReactNode } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LucideIcon } from "lucide-react";

interface CreationMethodCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  children: ReactNode;
  actionLabel: string;
  onAction: () => void;
  isLoading?: boolean;
}

export function CreationMethodCard({
  icon: Icon,
  title,
  description,
  children,
  actionLabel,
  onAction,
  isLoading = false,
}: CreationMethodCardProps) {
  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Icon className="h-6 w-6 text-primary" />
        </div>
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        <CardDescription className="text-sm">{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">{children}</div>
        <Button 
          className="w-full h-10 text-sm" 
          onClick={onAction}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Processing...
            </>
          ) : (
            actionLabel
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
