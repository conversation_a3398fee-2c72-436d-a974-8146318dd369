/**
 * AI Prompt Interface Component
 * Provides a ChatGPT-style interface for AI contract generation
 */

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Sparkles, Send, Loader2 } from "lucide-react";

interface AIPromptInterfaceProps {
  prompt: string;
  isGenerating: boolean;
  onPromptChange: (prompt: string) => void;
  onGenerate: () => void;
  canGenerate: boolean;
}

export function AIPromptInterface({
  prompt,
  isGenerating,
  onPromptChange,
  onGenerate,
  canGenerate,
}: AIPromptInterfaceProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      onGenerate();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
            <Sparkles className="h-4 w-4 text-purple-600" />
          </div>
          <h2 className="text-lg font-medium">AI-Assisted</h2>
          <Badge className="bg-amber-100 text-amber-800 border-amber-300 text-xs py-0.5 px-2">
            Coming Soon
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Generate contracts from simple descriptions
        </p>
      </div>

      {/* Main Prompt Interface */}
      <div className="flex flex-col items-center justify-center min-h-[500px]">
        <div className="w-full max-w-4xl">
          <div className="relative">
            <Textarea
              placeholder="Type your idea and we'll build it together."
              className="w-full min-h-[180px] text-sm p-8 pr-20 rounded-2xl border-2 border-muted-foreground/20 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 resize-none bg-background/50 backdrop-blur-sm"
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              onKeyDown={handleKeyDown}
            />

            {/* Action Buttons */}
            <div className="absolute bottom-4 right-4 flex items-center gap-2">
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 rounded-full hover:bg-muted"
                onClick={() => {/* Attach files functionality */}}
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
              </Button>

              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 rounded-full hover:bg-muted"
                onClick={() => {/* Star/favorite functionality */}}
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </Button>

              {/* Send Button */}
              <Button
                size="sm"
                onClick={onGenerate}
                disabled={!canGenerate || isGenerating}
                className="h-8 w-8 p-0 rounded-full bg-purple-500 hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 animate-spin text-white" />
                ) : (
                  <Send className="h-4 w-4 text-white" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Coming Soon Notice */}
        <div className="text-center mt-8">
          <p className="text-xs text-muted-foreground">
            ✨ Advanced AI contract generation coming soon
          </p>
        </div>
      </div>
    </div>
  );
}
