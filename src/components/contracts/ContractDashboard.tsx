/**
 * Refactored Contract Dashboard Component
 * Now uses smaller, focused components and custom hooks for better maintainability
 */

import ContractSummary from "./ContractSummary";
import { useContractDashboard } from "@/hooks/useContractDashboard";
import { DashboardStats } from "./dashboard/DashboardStats";
import { RecentContracts } from "./dashboard/RecentContracts";
import { ExpiringContracts } from "./dashboard/ExpiringContracts";
import { PendingApprovals } from "./dashboard/PendingApprovals";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface ContractDashboardProps {
  onCreateContract: () => void;
  onViewContract: (contractId: string) => void;
}

const ContractDashboard = ({
  onCreateContract,
  onViewContract,
}: ContractDashboardProps) => {
  // Use the custom hook for dashboard data
  const { dashboardData, loading, error } = useContractDashboard();

  // Error state display
  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Dashboard Statistics */}
      <DashboardStats
        stats={dashboardData.stats}
        loading={loading}
        onCreateContract={onCreateContract}
      />

      {/* Main Dashboard Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Recent Contracts */}
        <RecentContracts
          contracts={dashboardData.recentContracts}
          loading={loading}
          onViewContract={onViewContract}
        />

        {/* Expiring Contracts */}
        <ExpiringContracts
          contracts={dashboardData.expiringContracts}
          loading={loading}
          onViewContract={onViewContract}
        />

        {/* Pending Approvals */}
        <PendingApprovals
          contracts={dashboardData.pendingApprovalContracts}
          loading={loading}
          onViewContract={onViewContract}
        />
      </div>

      {/* Contract Summary - Show only if we have contracts */}
      {dashboardData.recentContracts.length > 0 && (
        <ContractSummary
          contractId={dashboardData.recentContracts[0].id}
          onViewDetails={() => onViewContract(dashboardData.recentContracts[0].id)}
        />
      )}
    </div>
  );
};

export default ContractDashboard;