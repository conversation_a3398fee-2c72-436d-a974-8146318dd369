/**
 * Pending Approvals Component
 * Displays contracts awaiting approval
 */

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, Users, ArrowUpRight, CheckCircle, XCircle } from "lucide-react";
import type { PendingApprovalContract } from "@/hooks/useContractDashboard";
import { formatDate } from "@/hooks/useContractDashboard";

interface PendingApprovalsProps {
  contracts: PendingApprovalContract[];
  loading: boolean;
  onViewContract: (contractId: string) => void;
}

export function PendingApprovals({ contracts, loading, onViewContract }: PendingApprovalsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-4 w-4" />
            Pending Approvals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-3 border rounded-lg">
                <div className="h-10 w-10 bg-muted animate-pulse rounded-full" />
                <div className="space-y-2 flex-1">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getApprovalStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-3 w-3 text-red-500" />;
      default:
        return <Clock className="h-3 w-3 text-yellow-500" />;
    }
  };

  const getApprovalStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'default';
      case 'rejected':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="mr-2 h-4 w-4" />
          Pending Approvals
        </CardTitle>
      </CardHeader>
      <CardContent>
        {contracts.length === 0 ? (
          <div className="text-center py-6">
            <CheckCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No pending approvals</p>
            <p className="text-xs text-muted-foreground mt-1">
              All contracts are up to date
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {contracts.map((contract) => (
              <div
                key={contract.id}
                className="flex items-start space-x-4 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage src={contract.submittedBy.avatar} />
                  <AvatarFallback>{contract.submittedBy.initials}</AvatarFallback>
                </Avatar>
                
                <div className="space-y-2 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {contract.title}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewContract(contract.id)}
                      className="h-8 w-8 p-0"
                    >
                      <ArrowUpRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {contract.type}
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>{contract.approvers.length} approver{contract.approvers.length !== 1 ? 's' : ''}</span>
                    </Badge>
                  </div>
                  
                  <div className="text-xs text-muted-foreground">
                    <p>Submitted by {contract.submittedBy.name}</p>
                    <p>on {formatDate(contract.submittedDate)}</p>
                  </div>
                  
                  {/* Approvers Status */}
                  <div className="space-y-1">
                    <p className="text-xs font-medium">Approvers:</p>
                    <div className="flex flex-wrap gap-1">
                      {contract.approvers.map((approver, index) => (
                        <Badge
                          key={index}
                          variant={getApprovalStatusColor(approver.status)}
                          className="flex items-center space-x-1 text-xs"
                        >
                          {getApprovalStatusIcon(approver.status)}
                          <span>{approver.name}</span>
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
