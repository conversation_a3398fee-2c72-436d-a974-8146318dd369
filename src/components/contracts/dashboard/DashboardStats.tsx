/**
 * Dashboard Statistics Component
 * Displays key contract metrics in a clean card layout
 */

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Plus,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { DashboardStats } from "@/hooks/useContractDashboard";

interface DashboardStatsProps {
  stats: DashboardStats;
  loading: boolean;
  onCreateContract: () => void;
}

export function DashboardStats({ stats, loading, onCreateContract }: DashboardStatsProps) {
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Contracts */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalContracts}</div>
          <p className="text-xs text-muted-foreground">
            Active contracts in workspace
          </p>
        </CardContent>
      </Card>

      {/* Pending Approvals */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.pendingApprovals}</div>
          <p className="text-xs text-muted-foreground">
            Awaiting approval
          </p>
        </CardContent>
      </Card>

      {/* Expiring Soon */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.expiringSoon}</div>
          <p className="text-xs text-muted-foreground">
            Within 30 days
          </p>
        </CardContent>
      </Card>

      {/* Compliance Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.complianceRate}%</div>
          <Progress value={stats.complianceRate} className="mt-2" />
          <p className="text-xs text-muted-foreground mt-1">
            Overall compliance
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Quick Actions Component
 * Provides easy access to common contract actions
 */
interface QuickActionsProps {
  onCreateContract: () => void;
}

export function QuickActions({ onCreateContract }: QuickActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={onCreateContract} className="w-full">
          <Plus className="mr-2 h-4 w-4" />
          Create New Contract
        </Button>
        
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm">
            Import Document
          </Button>
          <Button variant="outline" size="sm">
            View Templates
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
