/**
 * Expiring Contracts Component
 * Displays contracts that are expiring soon
 */

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Calendar, ArrowUpRight } from "lucide-react";
import type { ExpiringContract } from "@/hooks/useContractDashboard";
import { formatDate } from "@/hooks/useContractDashboard";

interface ExpiringContractsProps {
  contracts: ExpiringContract[];
  loading: boolean;
  onViewContract: (contractId: string) => void;
}

export function ExpiringContracts({ contracts, loading, onViewContract }: ExpiringContractsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="mr-2 h-4 w-4" />
            Expiring Soon
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-2 flex-1">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                </div>
                <div className="h-6 w-16 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getUrgencyColor = (daysRemaining: number) => {
    if (daysRemaining <= 7) return "destructive";
    if (daysRemaining <= 14) return "secondary";
    return "outline";
  };

  const getUrgencyIcon = (daysRemaining: number) => {
    if (daysRemaining <= 7) return <AlertTriangle className="h-3 w-3" />;
    return <Calendar className="h-3 w-3" />;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <AlertTriangle className="mr-2 h-4 w-4" />
          Expiring Soon
        </CardTitle>
      </CardHeader>
      <CardContent>
        {contracts.length === 0 ? (
          <div className="text-center py-6">
            <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No contracts expiring soon</p>
            <p className="text-xs text-muted-foreground mt-1">
              All contracts are up to date
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {contracts.map((contract) => (
              <div
                key={contract.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="space-y-1 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {contract.title}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewContract(contract.id)}
                      className="h-8 w-8 p-0"
                    >
                      <ArrowUpRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {contract.type}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {contract.counterparty}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>Expires {formatDate(contract.expiryDate)}</span>
                    </div>
                    
                    <Badge 
                      variant={getUrgencyColor(contract.daysRemaining)}
                      className="flex items-center space-x-1"
                    >
                      {getUrgencyIcon(contract.daysRemaining)}
                      <span>
                        {contract.daysRemaining} day{contract.daysRemaining !== 1 ? 's' : ''}
                      </span>
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
