/**
 * Recent Contracts Component
 * Displays the most recently created contracts
 */

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ArrowUpRight } from "lucide-react";
import type { RecentContract } from "@/hooks/useContractDashboard";
import { formatDate, getStatusBadgeVariant } from "@/hooks/useContractDashboard";

interface RecentContractsProps {
  contracts: RecentContract[];
  loading: boolean;
  onViewContract: (contractId: string) => void;
}

export function RecentContracts({ contracts, loading, onViewContract }: RecentContractsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Contracts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-10 w-10 bg-muted animate-pulse rounded-full" />
                <div className="space-y-2 flex-1">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-24 bg-muted animate-pulse rounded" />
                </div>
                <div className="h-6 w-16 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Contracts</CardTitle>
      </CardHeader>
      <CardContent>
        {contracts.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground">No recent contracts found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {contracts.map((contract) => (
              <div key={contract.id} className="flex items-center space-x-4">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={contract.createdBy.avatar} />
                  <AvatarFallback>{contract.createdBy.initials}</AvatarFallback>
                </Avatar>
                
                <div className="space-y-1 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {contract.title}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewContract(contract.id)}
                      className="h-8 w-8 p-0"
                    >
                      <ArrowUpRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant={getStatusBadgeVariant(contract.status)}>
                      {contract.status.replace('_', ' ')}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {contract.type}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Created by {contract.createdBy.name}</span>
                    <span>{formatDate(contract.createdDate)}</span>
                  </div>
                  
                  <p className="text-xs text-muted-foreground">
                    Counterparty: {contract.counterparty}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
