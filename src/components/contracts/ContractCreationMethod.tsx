/**
 * Refactored Contract Creation Method Component
 * Now uses smaller, focused components and custom hooks for better maintainability
 */

import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import UnifiedImportModal from "../modals/UnifiedImportModal";
import { ArrowLeft, FileText, Wand2, Sparkles, Check, BookTemplate, Lightbulb, Copy, Users } from "lucide-react";

// Reusable components
import { CreationMethodCard } from "./creation/CreationMethodCard";
import { TemplateCategoriesGrid } from "./creation/TemplateCategoryCard";
import { FeatureList } from "./creation/FeatureList";
import { WizardSteps } from "./creation/WizardSteps";
import { AIPromptInterface } from "./creation/AIPromptInterface";
import { SecondaryOptions } from "./creation/SecondaryOptions";

// Custom hook
import { useContractCreation } from "@/hooks/useContractCreation";

const ContractCreationMethod = () => {
  // Use the custom hook for all business logic
  const {
    activeTab,
    aiPrompt,
    isGenerating,
    showImportModal,
    canGenerateAI,
    handleUseTemplate,
    handleStartFromScratch,
    handleGoBack,
    handleImportModalChange,
    handleAIPromptChange,
    handleAIGenerate,
    handleTabChange,
    handleCategorySelect,
  } = useContractCreation();

  // Define feature lists for reuse
  const templateFeatures = [
    { icon: Check, label: "Legally vetted", color: "text-green-600" },
    { icon: BookTemplate, label: "Fully customizable", color: "text-blue-600" },
    { icon: Lightbulb, label: "Smart suggestions", color: "text-amber-600" },
  ];

  const scratchFeatures = [
    { icon: Wand2, label: "Step-by-step guidance", color: "text-purple-600" },
    { icon: Copy, label: "Clause library", color: "text-blue-600" },
    { icon: Users, label: "Collaboration ready", color: "text-green-600" },
  ];



  return (
    <div className="w-full h-full bg-background p-4 overflow-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold">Create Contract</h1>
          <p className="text-base text-muted-foreground mt-1">
            Choose how you&apos;d like to get started
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center h-9"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>




      {/* Main Content */}
      <div className="max-w-4xl mx-auto">
        <Tabs
          defaultValue="template"
          className="w-full"
          value={activeTab}
          onValueChange={handleTabChange}
        >
          <TabsList className="grid w-full grid-cols-3 mb-8 h-12">
            <TabsTrigger value="template" className="flex items-center gap-2 text-sm h-10">
              <FileText className="h-5 w-5" />
              <span>Templates</span>
            </TabsTrigger>
            <TabsTrigger value="scratch" className="flex items-center gap-2 text-sm h-10">
              <Wand2 className="h-5 w-5" />
              <span>Custom</span>
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2 text-sm h-10">
              <Sparkles className="h-5 w-5" />
              <span>AI Generate</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="template" className="space-y-6">
            <CreationMethodCard
              icon={FileText}
              title="Use Template"
              description="Start with professionally drafted templates"
              actionLabel="Browse All Templates"
              onAction={handleUseTemplate}
            >
              <TemplateCategoriesGrid onCategorySelect={handleCategorySelect} />
              <FeatureList features={templateFeatures} className="mb-4" />
            </CreationMethodCard>
          </TabsContent>

          <TabsContent value="scratch" className="space-y-6">
            <CreationMethodCard
              icon={Wand2}
              title="Start from Scratch"
              description="Build custom contracts with guided steps"
              actionLabel="Start Building Contract"
              onAction={handleStartFromScratch}
            >
              <WizardSteps />
              <FeatureList features={scratchFeatures} className="mb-4" />
            </CreationMethodCard>
          </TabsContent>

          <TabsContent value="ai" className="space-y-6">
            <AIPromptInterface
              prompt={aiPrompt}
              isGenerating={isGenerating}
              onPromptChange={handleAIPromptChange}
              onGenerate={handleAIGenerate}
              canGenerate={canGenerateAI}
            />
          </TabsContent>
        </Tabs>

        {/* Secondary Options */}
        <SecondaryOptions
          onImport={() => handleImportModalChange(true)}
          onClone={() => console.log("Clone functionality coming soon")}
          onRequestHelp={() => console.log("Request help functionality coming soon")}
        />
      </div>

      {/* Import Document Modal */}
      <UnifiedImportModal
        open={showImportModal}
        onOpenChange={handleImportModalChange}
        redirectToWizard={true}
      />


    </div>
  );
};



export default ContractCreationMethod;