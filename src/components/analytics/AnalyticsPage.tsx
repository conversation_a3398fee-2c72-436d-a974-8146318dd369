import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  FileText,
  PieChart,
  TrendingUp,
  Users,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Filter,
  Building2,
  Activity,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ContractActivityChart from "./ContractActivityChart";
import ContractTypesChart from "./ContractTypesChart";
import ActivityHistory from "../activity/ActivityHistory";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useApi, AnalyticsService } from "@/lib/api-client";
import type { AnalyticsSummary, PerformanceMetric } from "@/services/api-types";

const AnalyticsPage = () => {
  const [timeRange, setTimeRange] = useState<string>("6months");

  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get current workspace from context
  const { currentWorkspace, getUserWorkspaces } = useClerkWorkspace();
  const workspaces = getUserWorkspaces();
  const { fetch } = useApi();

  // Update analytics data when workspace changes
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      if (!currentWorkspace?.id) {
        setSummary(null);
        setPerformanceMetrics([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Fetch summary data
        const summaryResult = await fetch(
          async (token) => AnalyticsService.getAnalyticsSummary(currentWorkspace.id, token),
          "Loading analytics summary...",
          "Failed to load analytics summary"
        );

        if (summaryResult) {
          setSummary(summaryResult);
        }

        // Fetch performance metrics
        const metricsResult = await fetch(
          async (token) => AnalyticsService.getPerformanceMetrics(currentWorkspace.id, token),
          "Loading performance metrics...",
          "Failed to load performance metrics"
        );

        if (metricsResult) {
          setPerformanceMetrics(metricsResult);
        }
      } catch (err) {
        console.error("Error fetching analytics data:", err);
        setError("Failed to load analytics data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [currentWorkspace?.id, fetch]);

  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium">Analytics Dashboard</h3>
        <div className="flex items-center gap-3">
          {/* Workspace selector */}
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <Select
              value={currentWorkspace?.id || ""}
              onValueChange={(value) => {
                const workspace = workspaces.find(w => w.id === value);
                if (workspace) {
                  // This will trigger a re-fetch of analytics data
                  window.location.href = `/app/analytics?workspace=${value}`;
                }
              }}
            >
              <SelectTrigger className="h-8 w-[180px]">
                <SelectValue placeholder="Select workspace" />
              </SelectTrigger>
              <SelectContent>
                {workspaces.length > 0 ? (
                  workspaces.map((workspace) => (
                    <SelectItem key={workspace.id} value={workspace.id}>
                      {workspace.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="none" disabled>
                    No accessible workspaces
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Time range selector */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="h-8 w-[150px]">
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30days">Last 30 days</SelectItem>
                <SelectItem value="3months">Last 3 months</SelectItem>
                <SelectItem value="6months">Last 6 months</SelectItem>
                <SelectItem value="1year">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button variant="outline" size="sm" className="h-8">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-0 space-y-6">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
              <p className="text-muted-foreground">Loading analytics data...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-12">
              <p className="text-destructive mb-2">Error: {error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
            </div>
          ) : !summary ? (
            <div className="flex items-center justify-center py-12">
              <p className="text-muted-foreground">No data available for the selected workspace</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Contracts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{summary.total_contracts}</div>
                  <div className="flex items-center text-xs text-green-600 mt-1">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>+{summary.change_from_last_month}% from last month</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Active Contracts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{summary.active_contracts}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round((summary.active_contracts / summary.total_contracts) * 100)}% of total
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Pending Approvals
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{summary.pending_approvals}</div>
                  <div className="flex items-center text-xs text-red-600 mt-1">
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                    <span>{summary.change_from_last_week} from last week</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Compliance Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{summary.compliance_rate}%</div>
                  <Progress value={summary.compliance_rate} className="h-1 mt-2" />
                </CardContent>
              </Card>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="col-span-1">
              <CardHeader>
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 text-primary mr-2" />
                  <CardTitle>Contract Activity</CardTitle>
                </div>
                <CardDescription>
                  Monthly contract creation and completion
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ContractActivityChart />
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <div className="flex items-center">
                  <PieChart className="h-5 w-5 text-primary mr-2" />
                  <CardTitle>Contract Types</CardTitle>
                </div>
                <CardDescription>Distribution by contract type</CardDescription>
              </CardHeader>
              <CardContent>
                <ContractTypesChart />
              </CardContent>
            </Card>
          </div>

          {performanceMetrics.length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-primary mr-2" />
                  <CardTitle>Performance Metrics</CardTitle>
                </div>
                <CardDescription>Key performance indicators for {currentWorkspace?.name}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {performanceMetrics.map((metric, index) => (
                    <div key={index}>
                      <h3 className="font-medium mb-2">{metric.name}</h3>
                      <p className="text-2xl font-bold">{metric.value}</p>
                      <p className="text-sm text-muted-foreground">
                        {metric.change}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="contracts" className="mt-0 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center">
                <FileText className="h-5 w-5 text-primary mr-2" />
                <CardTitle>Contract Analytics</CardTitle>
              </div>
              <CardDescription>
                Detailed contract metrics and analysis for {currentWorkspace?.name || "selected workspace"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!currentWorkspace ? (
                <div className="h-[400px] flex items-center justify-center">
                  <p className="text-muted-foreground">
                    Please select a workspace to view contract analytics
                  </p>
                </div>
              ) : (
                <div className="h-[400px] flex items-center justify-center">
                  <p className="text-muted-foreground">
                    Contract analytics visualization for {currentWorkspace.name} would appear here
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="mt-0 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24</div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  <span>+12% from last month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Average Response Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.4 days</div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  <span>15% faster than last month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Contracts per User
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.3</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Average contracts managed per user
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  User Engagement
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">76%</div>
                <Progress value={76} className="h-1 mt-2" />
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-primary mr-2" />
                  <CardTitle>Activity History</CardTitle>
                </div>
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="h-8 w-[150px]">
                    <SelectValue placeholder="Select range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">Last 7 days</SelectItem>
                    <SelectItem value="30days">Last 30 days</SelectItem>
                    <SelectItem value="3months">Last 3 months</SelectItem>
                    <SelectItem value="6months">Last 6 months</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <CardDescription>
                Track all activities in your workspace
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!currentWorkspace ? (
                <div className="h-[400px] flex items-center justify-center">
                  <p className="text-muted-foreground">
                    Please select a workspace to view activity history
                  </p>
                </div>
              ) : (
                <ActivityHistory
                  activities={[
                    {
                      id: "activity-1",
                      type: "create",
                      entityType: "contract",
                      entityId: "contract-123",
                      entityName: "Service Agreement - Acme Corp",
                      user: {
                        id: "user-1",
                        name: "John Smith",
                        initials: "JS",
                      },
                      timestamp: "2023-07-15T10:30:00Z",
                    },
                    {
                      id: "activity-2",
                      type: "update",
                      entityType: "contract",
                      entityId: "contract-123",
                      entityName: "Service Agreement - Acme Corp",
                      user: {
                        id: "user-2",
                        name: "Jane Doe",
                        initials: "JD",
                      },
                      timestamp: "2023-07-15T14:45:00Z",
                      details: "Updated payment terms and added liability clause",
                    },
                    {
                      id: "activity-3",
                      type: "approve",
                      entityType: "contract",
                      entityId: "contract-123",
                      entityName: "Service Agreement - Acme Corp",
                      user: {
                        id: "user-3",
                        name: "Alice Johnson",
                        initials: "AJ",
                      },
                      timestamp: "2023-07-16T09:15:00Z",
                      details: "Approved the contract",
                    },
                    {
                      id: "activity-4",
                      type: "share",
                      entityType: "contract",
                      entityId: "contract-456",
                      entityName: "NDA - XYZ Inc",
                      user: {
                        id: "user-2",
                        name: "Jane Doe",
                        initials: "JD",
                      },
                      timestamp: "2023-07-17T11:20:00Z",
                      details: "Shared with external party",
                    },
                    {
                      id: "activity-5",
                      type: "comment",
                      entityType: "contract",
                      entityId: "contract-789",
                      entityName: "Lease Agreement - ABC Properties",
                      user: {
                        id: "user-4",
                        name: "Robert Brown",
                        initials: "RB",
                      },
                      timestamp: "2023-07-18T13:45:00Z",
                      details: "Added comment on payment schedule",
                    },
                  ]}
                  onViewEntity={(entityType, entityId) => console.log("View entity:", entityType, entityId)}
                />
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center">
                <Users className="h-5 w-5 text-primary mr-2" />
                <CardTitle>User Performance</CardTitle>
              </div>
              <CardDescription>
                Performance metrics for users in {currentWorkspace?.name || "selected workspace"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!currentWorkspace ? (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">
                    Please select a workspace to view user performance
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <div>
                    <h3 className="font-medium mb-2">Top Performers</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">JS</div>
                          <span className="text-sm">John Smith</span>
                        </div>
                        <span className="text-sm font-medium">32 contracts</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">JD</div>
                          <span className="text-sm">Jane Doe</span>
                        </div>
                        <span className="text-sm font-medium">28 contracts</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">AJ</div>
                          <span className="text-sm">Alice Johnson</span>
                        </div>
                        <span className="text-sm font-medium">24 contracts</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Response Time</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">RB</div>
                          <span className="text-sm">Robert Brown</span>
                        </div>
                        <span className="text-sm font-medium">0.8 days</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">AJ</div>
                          <span className="text-sm">Alice Johnson</span>
                        </div>
                        <span className="text-sm font-medium">1.2 days</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">JS</div>
                          <span className="text-sm">John Smith</span>
                        </div>
                        <span className="text-sm font-medium">1.5 days</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Approval Rate</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">JD</div>
                          <span className="text-sm">Jane Doe</span>
                        </div>
                        <span className="text-sm font-medium">98%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">AJ</div>
                          <span className="text-sm">Alice Johnson</span>
                        </div>
                        <span className="text-sm font-medium">95%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">RB</div>
                          <span className="text-sm">Robert Brown</span>
                        </div>
                        <span className="text-sm font-medium">92%</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsPage;
