from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import httpx
from jose import jwt, JW<PERSON>rror
from app.core.config import settings
from app.db.database import get_supabase_client
from app.utils.response import forbidden_error, validation_error, server_error
import logging
from typing import Optional

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Development mode authentication bypass
async def get_development_user() -> dict:
    """
    Return a mock user for development when <PERSON> is not properly configured.
    """
    return {
        "sub": "user_2qKJKJKJKJKJKJKJKJKJKJKJKJKJ",  # Use a realistic Clerk user ID format
        "email": "<EMAIL>",
        "name": "<PERSON> Womade",
        "first_name": "<PERSON>",
        "last_name": "Womade",
        "iss": "development",
        "iat": 1640995200,  # Mock timestamp
        "exp": 9999999999   # Far future expiration
    }

def validate_clerk_config():
    """
    Validate that Clerk configuration is properly set up.
    """
    if not settings.CLERK_SECRET_KEY or settings.CLERK_SECRET_KEY == "sk_test_placeholder_get_from_clerk_dashboard":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Clerk secret key not configured. Please set CLERK_SECRET_KEY in your environment variables."
        )

    if not settings.CLERK_PUBLISHABLE_KEY:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Clerk publishable key not configured. Please set CLERK_PUBLISHABLE_KEY in your environment variables."
        )

async def verify_clerk_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Verify the JWT token from Clerk.
    """
    # Check if Clerk is misconfigured
    is_development = settings.ENVIRONMENT == "development"
    is_clerk_misconfigured = (not settings.CLERK_SECRET_KEY or
                             settings.CLERK_SECRET_KEY == "sk_test_placeholder_get_from_clerk_dashboard" or
                             settings.CLERK_SECRET_KEY == "test_clerk_secret_key")

    # Only use development bypass for specific development token or when Clerk is misconfigured
    token = credentials.credentials
    if token == "dev-token":
        logger.warning("Using development authentication bypass - dev-token detected")
        return await get_development_user()

    if is_clerk_misconfigured:
        logger.warning("Using development authentication bypass - Clerk not properly configured")
        return await get_development_user()

    # Validate configuration for production
    validate_clerk_config()

    try:
        # Try to decode the token without signature verification first
        # This allows us to inspect the token structure
        try:
            payload = jwt.decode(
                token,
                options={"verify_signature": False, "verify_exp": False}
            )
            logger.info(f"Token payload: {payload}")
        except JWTError as e:
            logger.warning(f"Could not decode token: {e}")
            # If we can't decode the token at all, fall back to development mode
            if is_development:
                logger.warning("Falling back to development mode due to token decode error")
                return await get_development_user()
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token format"
            )

        # Extract the instance ID from the publishable key
        # Format: pk_test_{instance_id}
        pub_key_parts = settings.CLERK_PUBLISHABLE_KEY.split('_')
        if len(pub_key_parts) < 3:
            if is_development:
                logger.warning("Invalid Clerk publishable key format, using development mode")
                return await get_development_user()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid Clerk publishable key format"
            )

        instance_id = pub_key_parts[2]

        # Fetch Clerk's JWKS (JSON Web Key Set)
        jwks_url = f"https://{instance_id}.clerk.accounts.dev/.well-known/jwks.json"

        async with httpx.AsyncClient() as client:
            try:
                jwks_response = await client.get(jwks_url, timeout=10.0)
                jwks_response.raise_for_status()
                jwks = jwks_response.json()
            except (httpx.RequestError, httpx.HTTPStatusError) as e:
                logger.error(f"Failed to fetch JWKS from {jwks_url}: {e}")
                if is_development:
                    logger.warning("JWKS fetch failed, using development mode")
                    return await get_development_user()
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Unable to verify authentication token. Please try again later."
                )

        # Validate required claims
        user_id = payload.get("sub")
        if not user_id:
            if is_development:
                logger.warning("Missing user ID in token, using development mode")
                return await get_development_user()
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token: missing user ID"
            )

        # Validate issuer (should match Clerk instance)
        expected_issuer = f"https://{instance_id}.clerk.accounts.dev"
        if payload.get("iss") != expected_issuer:
            if is_development:
                logger.warning(f"Invalid issuer in token (expected: {expected_issuer}, got: {payload.get('iss')}), using development mode")
                return await get_development_user()
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token: invalid issuer"
            )

        return payload

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {e}")
        if is_development:
            logger.warning("Unexpected error during token verification, using development mode")
            return await get_development_user()
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication verification failed"
        )

async def validate_workspace_access(user_id: str, workspace_id: str, supabase) -> dict:
    """
    Validate that a user has access to a specific workspace.
    Returns the workspace membership data if valid, raises HTTPException if not.
    """
    if not workspace_id:
        raise validation_error("Workspace ID is required")

    # Check if user is a member of this workspace
    member_check = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

    member_has_error = hasattr(member_check, 'error') and member_check.error
    member_has_data = hasattr(member_check, 'data') and member_check.data

    if member_has_error:
        logger.error(f"Error checking workspace membership: {member_check.error}")
        raise server_error("Failed to validate workspace access")

    if not member_has_data or not member_check.data:
        raise forbidden_error(f"Access denied: You don't have permission to access workspace {workspace_id}")

    membership_data = member_check.data[0]

    # Additional security checks
    if not membership_data.get("is_active", True):
        raise forbidden_error("Your workspace membership has been deactivated")

    # Check if workspace itself is active
    workspace_check = supabase.table("workspaces").select("is_active").eq("id", workspace_id).execute()
    if workspace_check.data and not workspace_check.data[0].get("is_active", True):
        raise forbidden_error("This workspace has been deactivated")

    return membership_data

async def get_current_user(request: Request, payload: dict = Depends(verify_clerk_token)):
    """
    Get the current user from the token payload and fetch additional details from the database.
    Also includes the active workspace ID if provided in the request header.
    """
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )

    # Get Supabase client
    supabase = get_supabase_client()

    try:
        # Check if user exists in the database
        user_response = supabase.table("users").select("*").eq("id", user_id).execute()

        user_data = {"id": user_id}

        # Handle Supabase response properly
        has_error = hasattr(user_response, 'error') and user_response.error
        has_data = hasattr(user_response, 'data') and user_response.data

        if not has_error and has_data:
            # User exists, use the data from the database
            user_data.update(user_response.data[0])
            logger.info(f"Found existing user: {user_id}")
        else:
            # User doesn't exist, create a new user record
            logger.info(f"Creating new user: {user_id}")

            # Extract user details from the token payload
            email = payload.get("email", "")
            name = payload.get("name", "")

            # Generate initials from name or email
            if name:
                initials = "".join([part[0].upper() for part in name.split()[:2] if part])
            elif email:
                initials = email[:2].upper()
            else:
                initials = user_id[:2].upper()

            new_user_data = {
                "id": user_id,
                "email": email,
                "first_name": name.split()[0] if name else "User",
                "last_name": " ".join(name.split()[1:]) if len(name.split()) > 1 else "",
                "initials": initials,
                "notification_preferences": {
                    "email_approvals": True,
                    "email_updates": True,
                    "email_reminders": True,
                    "email_comments": True,
                    "system_approvals": True,
                    "browser_notifications": True,
                    "email_digest_frequency": "daily"
                }
            }

            # Insert the user into the database
            try:
                create_response = supabase.table("users").insert(new_user_data).execute()
                create_has_error = hasattr(create_response, 'error') and create_response.error
                create_has_data = hasattr(create_response, 'data') and create_response.data

                if create_has_error:
                    logger.error(f"Error creating user in database: {create_response.error}")
                    # Continue with basic user data even if database insert fails
                    user_data.update(new_user_data)
                elif create_has_data:
                    user_data.update(create_response.data[0])
                    logger.info(f"Successfully created user: {user_id}")
                else:
                    # No error but no data either, use the new_user_data
                    user_data.update(new_user_data)
                    logger.info(f"Created user with basic data: {user_id}")
            except Exception as e:
                logger.error(f"Exception creating user: {e}")
                # Continue with basic user data
                user_data.update(new_user_data)

        # Check for workspace ID in the request header
        workspace_id = request.headers.get("X-Workspace-ID")

        if workspace_id:
            try:
                # Validate workspace access using the new validation function
                workspace_membership = await validate_workspace_access(user_id, workspace_id, supabase)

                # User has access to this workspace
                user_data["active_workspace_id"] = workspace_id
                user_data["workspace_role"] = workspace_membership.get("role_id")
                user_data["workspace_membership"] = workspace_membership
                logger.info(f"User {user_id} accessing workspace {workspace_id} with role {workspace_membership.get('role_id')}")

            except HTTPException as e:
                # Log the access attempt but don't fail the authentication
                # This allows the user to still access the app but without workspace context
                logger.warning(f"User {user_id} attempted to access unauthorized workspace {workspace_id}: {e.detail}")
                # Don't set active_workspace_id if access is denied

        # Get user's workspaces
        workspaces_response = supabase.table("workspace_members").select("workspace_id,role_id").eq("user_id", user_id).execute()

        workspaces_has_error = hasattr(workspaces_response, 'error') and workspaces_response.error
        workspaces_has_data = hasattr(workspaces_response, 'data') and workspaces_response.data

        if not workspaces_has_error and workspaces_has_data:
            user_data["workspaces"] = workspaces_response.data
        else:
            user_data["workspaces"] = []

        return user_data

    except Exception as e:
        logger.error(f"Error in get_current_user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )

async def get_current_user_with_workspace(
    request: Request,
    workspace_id: str,
    payload: dict = Depends(verify_clerk_token)
):
    """
    Get the current user and validate they have access to the specified workspace.
    This is a stricter version that requires workspace access.
    """
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )

    # Get Supabase client
    supabase = get_supabase_client()

    try:
        # Validate workspace access first
        workspace_membership = await validate_workspace_access(user_id, workspace_id, supabase)

        # Get user data
        user_response = supabase.table("users").select("*").eq("id", user_id).execute()
        user_data = {"id": user_id}

        # Handle Supabase response properly
        has_error = hasattr(user_response, 'error') and user_response.error
        has_data = hasattr(user_response, 'data') and user_response.data

        if not has_error and has_data:
            user_data.update(user_response.data[0])
        else:
            # Create basic user data if not found in database
            user_data.update({
                "email": payload.get("email", ""),
                "first_name": payload.get("name", "User").split()[0],
                "last_name": " ".join(payload.get("name", "").split()[1:]) if len(payload.get("name", "").split()) > 1 else "",
            })

        # Add workspace context
        user_data["active_workspace_id"] = workspace_id
        user_data["workspace_role"] = workspace_membership.get("role_id")
        user_data["workspace_membership"] = workspace_membership

        return user_data

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error in get_current_user_with_workspace: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )
